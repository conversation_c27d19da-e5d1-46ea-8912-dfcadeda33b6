package impl

import (
	"context"
	"fmt"
	"math"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	"pxpat-backend/internal/content-cluster/interaction-service/service"
	repositoryErrors "pxpat-backend/pkg/errors/repository"
)

// favoriteItemServiceImpl 收藏项服务实现
type favoriteItemServiceImpl struct {
	favoriteItemRepo repository.FavoriteItemRepository
	userClient       client.UserServiceClient
	videoClient      client.VideoServiceClient
	novelClient      client.NovelServiceClient
	musicClient      client.MusicServiceClient
}

// NewFavoriteItemService 创建收藏项服务实例
func NewFavoriteItemService(
	favoriteItemRepo repository.FavoriteItemRepository,
	userClient client.UserServiceClient,
	videoClient client.VideoServiceClient,
	novelClient client.NovelServiceClient,
	musicClient client.MusicServiceClient,
) service.FavoriteItemService {
	return &favoriteItemServiceImpl{
		favoriteItemRepo: favoriteItemRepo,
		userClient:       userClient,
		videoClient:      videoClient,
		novelClient:      novelClient,
		musicClient:      musicClient,
	}
}

// getContentInfoByType 根据内容类型从相应的服务获取内容信息
func (s *favoriteItemServiceImpl) getContentInfoByType(contentKSUID, contentType string) (*client.ContentInfo, error) {
	switch contentType {
	case "video", "anime", "short":
		// 视频、动漫、短视频都通过video服务处理
		return s.videoClient.GetContentByKSUID(contentKSUID)
	case "novel":
		// 小说通过novel服务处理
		return s.novelClient.GetContentByKSUID(contentKSUID)
	case "music":
		// 音乐通过music服务处理
		return s.musicClient.GetContentByKSUID(contentKSUID)
	default:
		return nil, fmt.Errorf("不支持的内容类型: %s", contentType)
	}
}

// AddToFavorite 添加到收藏夹
func (s *favoriteItemServiceImpl) AddToFavorite(ctx context.Context, userKSUID string, req *dto.AddToFavoriteRequest) error {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Strs("folder_ids", req.FavoriteFolderIDs).
		Msg("Adding content to favorite")

	// 根据内容类型验证内容是否存在
	content, err := s.getContentInfoByType(req.ContentKSUID, req.ContentType)
	if err != nil {
		log.Error().Err(err).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("Content not found")
		return fmt.Errorf("content not found: %w", err)
	}

	// 验证内容类型
	contentType := model.ContentType(req.ContentType)
	if !model.IsValidContentType(contentType) {
		if content != nil && content.ContentType != "" {
			contentType = model.ContentType(content.ContentType) // 使用内容服务返回的类型
		} else {
			return repositoryErrors.ErrInvalidContentType
		}
	}

	// 如果没有指定收藏夹，添加到默认收藏夹
	folderIDs := req.FavoriteFolderIDs
	if len(folderIDs) == 0 {
		folderIDs = []string{""} // 空字符串表示使用默认收藏夹
	}

	// 检查重复收藏
	nonDuplicateFolderIDs, err := s.checkDuplicateFavorites(ctx, userKSUID, req.ContentKSUID, folderIDs)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to check duplicate favorites")
		return err
	}

	if len(nonDuplicateFolderIDs) == 0 {
		// 所有收藏夹都已存在该内容
		log.Info().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Content already favorited in all specified folders")
		return nil
	}

	// 批量添加到收藏夹
	for _, folderID := range nonDuplicateFolderIDs {
		// 创建收藏项
		item := model.NewFavoriteItem(userKSUID, req.ContentKSUID, model.ContentType(req.ContentType), folderID)
		err := s.favoriteItemRepo.BatchCreate(ctx, []*model.FavoriteItem{item})
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Str("folder_id", folderID).
				Msg("Failed to add to favorite")
			return err
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Int("added_count", len(nonDuplicateFolderIDs)).
		Msg("Content added to favorites successfully")

	return nil
}

// RemoveFromFavorite 从收藏夹移除
func (s *favoriteItemServiceImpl) RemoveFromFavorite(ctx context.Context, userKSUID string, req *dto.RemoveFromFavoriteRequest) error {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("Removing content from favorite")

	// 从收藏中移除内容
	err := s.favoriteItemRepo.DeleteByContentKSUID(ctx, userKSUID, req.ContentKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to remove from favorite")
		return fmt.Errorf("failed to remove from favorite: %w", err)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("Content removed from favorites successfully")

	return nil
}

// MoveFavoriteItem 移动收藏项
func (s *favoriteItemServiceImpl) MoveFavoriteItem(ctx context.Context, userKSUID string, req *dto.MoveFavoriteItemRequest) (*dto.BatchFavoriteOperationResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("item_id", req.FavoriteItemID).
		Str("target_folder_id", req.TargetFolderID).
		Msg("Moving favorite item")

	// 移动单个收藏项
	if req.FavoriteItemID != "" {
		err := s.favoriteItemRepo.MoveTo(ctx, req.FavoriteItemID, userKSUID, req.TargetFolderID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("item_id", req.FavoriteItemID).
				Msg("Failed to move favorite item")
			return nil, fmt.Errorf("failed to move item: %w", err)
		}

		return &dto.BatchFavoriteOperationResponse{
			Success:        true,
			TotalRequested: 1,
			SuccessCount:   1,
			SkippedCount:   0,
		}, nil
	}

	// 批量移动收藏项
	if len(req.TargetFolderIDs) > 0 {
		// 这里需要实现批量移动逻辑
		// 暂时返回错误，需要扩展repository接口
		return nil, fmt.Errorf("batch move not implemented yet")
	}

	return nil, fmt.Errorf("no items specified for move operation")
}

// GetFavoriteItems 获取收藏项列表
func (s *favoriteItemServiceImpl) GetFavoriteItems(ctx context.Context, currentUserKSUID string, req *dto.GetFavoriteItemsRequest) (*dto.GetFavoriteItemsResponse, error) {
	log.Info().
		Str("current_user_ksuid", currentUserKSUID).
		Str("target_user_ksuid", req.UserKSUID).
		Str("folder_id", req.FavoriteFolderID).
		Str("content_type", req.ContentType).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("Getting favorite items")

	// 检查隐私设置：如果不是本人访问，需要检查隐私设置
	if currentUserKSUID != req.UserKSUID {
		if err := s.checkPrivacyAccess(ctx, currentUserKSUID, req.UserKSUID); err != nil {
			return nil, err
		}
	}

	var items []*model.FavoriteItem
	var total int64
	var err error

	// 根据查询条件获取收藏项
	if req.ContentType != "" {
		// 按收藏夹和内容类型查询
		items, total, err = s.favoriteItemRepo.GetByFolderAndContentType(ctx, req.UserKSUID, req.FavoriteFolderID, model.ContentType(req.ContentType), req.Page, req.PageSize)
	} else {
		// 按收藏夹查询
		items, total, err = s.favoriteItemRepo.GetByFolderKSUID(ctx, req.UserKSUID, req.FavoriteFolderID, req.Page, req.PageSize)
	}

	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("Failed to get favorite items")
		return nil, fmt.Errorf("failed to get favorite items: %w", err)
	}

	// 转换为响应格式
	var itemResponses []*dto.FavoriteItemResponse
	for _, item := range items {
		itemResponses = append(itemResponses, s.toItemResponse(item))
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	response := &dto.GetFavoriteItemsResponse{
		Items:      itemResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Int64("total", total).
		Int("returned", len(itemResponses)).
		Msg("Favorite items retrieved successfully")

	return response, nil
}

// CheckFavoriteStatus 检查收藏状态
func (s *favoriteItemServiceImpl) CheckFavoriteStatus(ctx context.Context, userKSUID string, req *dto.CheckFavoriteStatusRequest) (*dto.CheckFavoriteStatusResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("Checking favorite status")

	// 获取用户对内容的收藏状态
	items, err := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	if err != nil {
		if err == repositoryErrors.ErrFavoriteItemNotFound {
			// 未收藏
			return &dto.CheckFavoriteStatusResponse{
				IsFavorited: false,
				Folders:     []*dto.FavoriteFolderResponse{},
				Items:       []*dto.FavoriteItemResponse{},
			}, nil
		}
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to check favorite status")
		return nil, fmt.Errorf("failed to check favorite status: %w", err)
	}

	// 已收藏，返回收藏信息
	itemResponses := make([]*dto.FavoriteItemResponse, len(items))
	for i, item := range items {
		itemResponses[i] = s.toItemResponse(item)
	}

	response := &dto.CheckFavoriteStatusResponse{
		IsFavorited: len(items) > 0,
		Items:       itemResponses,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Bool("is_favorited", true).
		Msg("Favorite status checked successfully")

	return response, nil
}

// ===== 辅助方法 =====

// checkDuplicateFavorites 检查重复收藏
func (s *favoriteItemServiceImpl) checkDuplicateFavorites(ctx context.Context, userKSUID, contentKSUID string, folderIDs []string) ([]string, error) {
	// 获取现有的收藏项
	existingItems, err := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, contentKSUID)
	if err != nil && err != repositoryErrors.ErrFavoriteItemNotFound {
		return nil, err
	}

	// 创建现有收藏夹ID的映射
	existingFolderIDs := make(map[string]bool)
	for _, item := range existingItems {
		existingFolderIDs[item.FavoriteFolderID] = true
	}

	var nonDuplicateIDs []string
	for _, folderID := range folderIDs {
		// 如果这个收藏夹中还没有收藏这个内容，则可以添加
		if !existingFolderIDs[folderID] {
			nonDuplicateIDs = append(nonDuplicateIDs, folderID)
		}
	}

	return nonDuplicateIDs, nil
}

// checkPrivacyAccess 检查隐私访问权限
func (s *favoriteItemServiceImpl) checkPrivacyAccess(ctx context.Context, currentUserKSUID, targetUserKSUID string) error {
	// 这里应该调用用户服务检查隐私设置
	// 暂时允许访问，后续实现
	return nil
}

// toItemResponse 转换收藏项模型为响应格式
func (s *favoriteItemServiceImpl) toItemResponse(item *model.FavoriteItem) *dto.FavoriteItemResponse {
	return &dto.FavoriteItemResponse{
		FavoriteItemID:   item.FavoriteItemID,
		UserKSUID:        item.UserKSUID,
		ContentKSUID:     item.ContentKSUID,
		ContentType:      string(item.ContentType),
		FavoriteFolderID: item.FavoriteFolderID,
		CreatedAt:        item.CreatedAt,
		UpdatedAt:        item.UpdatedAt,
	}
}
