package client

import (
	"fmt"
	"time"

	"pxpat-backend/pkg/httpclient"

	"github.com/rs/zerolog/log"
)

// InteractionServiceClient 交互服务客户端接口
type InteractionServiceClient interface {
	// CheckFavoriteStatus 检查用户收藏状态
	CheckFavoriteStatus(userKSUID, contentKSUID string) (bool, error)
	// GetContentFavoriteCount 获取内容收藏数
	GetContentFavoriteCount(contentKSUID string) (int64, error)

	// Like相关方法
	// CheckUserLikeStatus 检查用户点赞状态
	CheckUserLikeStatus(userKSUID, contentKSUID string) (likeType string, exists bool, err error)
	// GetContentLikeCounts 获取内容点赞统计
	GetContentLikeCounts(contentKSUID string) (likeCount, dislikeCount int64, err error)
	// BatchGetContentLikeStats 批量获取内容点赞统计
	BatchGetContentLikeStats(contentKSUIDs []string) (map[string]ContentLikeStatsItem, error)
	// BatchCheckUserLikeStatus 批量检查用户点赞状态
	BatchCheckUserLikeStatus(userKSUID string, contentKSUIDs []string) (map[string]UserLikeStatusItem, error)


}

// interactionServiceClient 交互服务客户端实现
type interactionServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// InteractionServiceConfig 交互服务客户端配置
type InteractionServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// CheckFavoriteStatusResponse 检查收藏状态响应
type CheckFavoriteStatusResponse struct {
	IsFavorited bool `json:"is_favorited"`
}

// GetContentFavoriteCountResponse 获取内容收藏数响应
type GetContentFavoriteCountResponse struct {
	Count int64 `json:"count"`
}

// CheckUserLikeStatusResponse 检查用户点赞状态响应
type CheckUserLikeStatusResponse struct {
	LikeType string `json:"like_type"` // 点赞类型：like/dislike，空字符串表示未点赞
	Exists   bool   `json:"exists"`    // 是否存在点赞记录
}

// GetContentLikeCountsResponse 获取内容点赞统计响应
type GetContentLikeCountsResponse struct {
	LikeCount    int64 `json:"like_count"`
	DislikeCount int64 `json:"dislike_count"`
}

// ContentLikeStatsItem 内容点赞统计项
type ContentLikeStatsItem struct {
	ContentKSUID string `json:"content_ksuid"`
	LikeCount    int64  `json:"like_count"`
	DislikeCount int64  `json:"dislike_count"`
}

// UserLikeStatusItem 用户点赞状态项
type UserLikeStatusItem struct {
	Type       string `json:"type"`        // 点赞类型：like/dislike，空字符串表示未点赞
	IsLiked    bool   `json:"is_liked"`    // 是否点赞
	IsDisliked bool   `json:"is_disliked"` // 是否不喜欢
}

// BatchGetContentLikeStatsRequest 批量获取内容点赞统计请求
type BatchGetContentLikeStatsRequest struct {
	ContentKSUIDs []string `json:"content_ksuids"`
}

// BatchGetContentLikeStatsResponse 批量获取内容点赞统计响应
type BatchGetContentLikeStatsResponse struct {
	Stats map[string]ContentLikeStatsItem `json:"stats"`
}

// BatchCheckUserLikeStatusRequest 批量检查用户点赞状态请求
type BatchCheckUserLikeStatusRequest struct {
	UserKSUID     string   `json:"user_ksuid"`
	ContentKSUIDs []string `json:"content_ksuids"`
}

// BatchCheckUserLikeStatusResponse 批量检查用户点赞状态响应
type BatchCheckUserLikeStatusResponse struct {
	Statuses map[string]UserLikeStatusItem `json:"statuses"`
}



// NewInteractionServiceClient 创建交互服务客户端
func NewInteractionServiceClient(config InteractionServiceConfig) InteractionServiceClient {
	return &interactionServiceClient{
		httpClient: httpclient.NewHTTPClient(httpclient.ClientConfig{
			BaseURL:          config.BaseURL,
			Timeout:          config.Timeout,
			RetryCount:       3,
			RetryWaitTime:    1 * time.Second,
			RetryMaxWaitTime: 5 * time.Second,
		}),
	}
}

// CheckFavoriteStatus 检查用户收藏状态
func (c *interactionServiceClient) CheckFavoriteStatus(userKSUID, contentKSUID string) (bool, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("调用交互服务检查收藏状态")

	var response httpclient.ServiceResponse[CheckFavoriteStatusResponse]
	requestURL := fmt.Sprintf("/api/v1/intra/favorites/%s/status/%s", userKSUID, contentKSUID)

	err := c.httpClient.Get(requestURL, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("url", requestURL).
			Msg("调用交互服务检查收藏状态API失败")
		return false, fmt.Errorf("failed to call interaction service check favorite status API: %w", err)
	}

	if !response.IsSuccess() {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("交互服务检查收藏状态返回错误")
		return false, nil // 返回false而不是错误，因为可能是用户不存在等正常情况
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Bool("is_favorited", response.Data.IsFavorited).
		Msg("交互服务检查收藏状态成功")

	return response.Data.IsFavorited, nil
}

// GetContentFavoriteCount 获取内容收藏数
func (c *interactionServiceClient) GetContentFavoriteCount(contentKSUID string) (int64, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("调用交互服务获取内容收藏数")

	var response httpclient.ServiceResponse[GetContentFavoriteCountResponse]
	requestURL := fmt.Sprintf("/api/v1/intra/favorites/content/%s/count", contentKSUID)

	err := c.httpClient.Get(requestURL, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Str("url", requestURL).
			Msg("调用交互服务获取内容收藏数API失败")
		return 0, fmt.Errorf("failed to call interaction service get content favorite count API: %w", err)
	}

	if !response.IsSuccess() {
		log.Warn().
			Str("content_ksuid", contentKSUID).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("交互服务获取内容收藏数返回错误")
		return 0, nil // 返回0而不是错误
	}

	log.Info().
		Str("content_ksuid", contentKSUID).
		Int64("count", response.Data.Count).
		Msg("交互服务获取内容收藏数成功")

	return response.Data.Count, nil
}

// CheckUserLikeStatus 检查用户点赞状态
func (c *interactionServiceClient) CheckUserLikeStatus(userKSUID, contentKSUID string) (likeType string, exists bool, err error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("调用交互服务检查用户点赞状态")

	var response httpclient.ServiceResponse[CheckUserLikeStatusResponse]
	requestURL := fmt.Sprintf("/internal/v1/like/status?user_ksuid=%s&content_ksuid=%s", userKSUID, contentKSUID)

	err = c.httpClient.Get(requestURL, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("url", requestURL).
			Msg("调用交互服务检查用户点赞状态API失败")
		return "", false, fmt.Errorf("failed to call interaction service check user like status API: %w", err)
	}

	if !response.IsSuccess() {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("交互服务检查用户点赞状态返回错误")
		return "", false, nil // 返回默认值而不是错误
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Str("like_type", response.Data.LikeType).
		Bool("exists", response.Data.Exists).
		Msg("交互服务检查用户点赞状态成功")

	return response.Data.LikeType, response.Data.Exists, nil
}

// GetContentLikeCounts 获取内容点赞统计
func (c *interactionServiceClient) GetContentLikeCounts(contentKSUID string) (likeCount, dislikeCount int64, err error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("调用交互服务获取内容点赞统计")

	var response httpclient.ServiceResponse[GetContentLikeCountsResponse]
	requestURL := fmt.Sprintf("/internal/v1/like/content/counts?content_ksuid=%s", contentKSUID)

	err = c.httpClient.Get(requestURL, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Str("url", requestURL).
			Msg("调用交互服务获取内容点赞统计API失败")
		return 0, 0, fmt.Errorf("failed to call interaction service get content like counts API: %w", err)
	}

	if !response.IsSuccess() {
		log.Warn().
			Str("content_ksuid", contentKSUID).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("交互服务获取内容点赞统计返回错误")
		return 0, 0, nil // 返回0而不是错误
	}

	log.Info().
		Str("content_ksuid", contentKSUID).
		Int64("like_count", response.Data.LikeCount).
		Int64("dislike_count", response.Data.DislikeCount).
		Msg("交互服务获取内容点赞统计成功")

	return response.Data.LikeCount, response.Data.DislikeCount, nil
}

// BatchGetContentLikeStats 批量获取内容点赞统计
func (c *interactionServiceClient) BatchGetContentLikeStats(contentKSUIDs []string) (map[string]ContentLikeStatsItem, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]ContentLikeStatsItem), nil
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Msg("调用交互服务批量获取内容点赞统计")

	var response httpclient.ServiceResponse[BatchGetContentLikeStatsResponse]
	requestURL := "/internal/v1/like/content/stats/batch"

	requestBody := BatchGetContentLikeStatsRequest{
		ContentKSUIDs: contentKSUIDs,
	}

	err := c.httpClient.Post(requestURL, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Int("content_count", len(contentKSUIDs)).
			Str("url", requestURL).
			Msg("调用交互服务批量获取内容点赞统计API失败")
		return nil, fmt.Errorf("failed to call interaction service batch get content like stats API: %w", err)
	}

	if !response.IsSuccess() {
		log.Warn().
			Int("content_count", len(contentKSUIDs)).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("交互服务批量获取内容点赞统计返回错误")
		return make(map[string]ContentLikeStatsItem), nil // 返回空map而不是错误
	}

	log.Info().
		Int("content_count", len(contentKSUIDs)).
		Int("result_count", len(response.Data.Stats)).
		Msg("交互服务批量获取内容点赞统计成功")

	return response.Data.Stats, nil
}

// BatchCheckUserLikeStatus 批量检查用户点赞状态
func (c *interactionServiceClient) BatchCheckUserLikeStatus(userKSUID string, contentKSUIDs []string) (map[string]UserLikeStatusItem, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]UserLikeStatusItem), nil
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("调用交互服务批量检查用户点赞状态")

	var response httpclient.ServiceResponse[BatchCheckUserLikeStatusResponse]
	requestURL := "/internal/v1/like/status/batch"

	requestBody := BatchCheckUserLikeStatusRequest{
		UserKSUID:     userKSUID,
		ContentKSUIDs: contentKSUIDs,
	}

	err := c.httpClient.Post(requestURL, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("content_count", len(contentKSUIDs)).
			Str("url", requestURL).
			Msg("调用交互服务批量检查用户点赞状态API失败")
		return nil, fmt.Errorf("failed to call interaction service batch check user like status API: %w", err)
	}

	if !response.IsSuccess() {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Int("content_count", len(contentKSUIDs)).
			Str("error_message", response.GetErrorMessage()).
			Int("response_code", response.Code).
			Msg("交互服务批量检查用户点赞状态返回错误")
		return make(map[string]UserLikeStatusItem), nil // 返回空map而不是错误
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("content_count", len(contentKSUIDs)).
		Int("result_count", len(response.Data.Statuses)).
		Msg("交互服务批量检查用户点赞状态成功")

	return response.Data.Statuses, nil
}






