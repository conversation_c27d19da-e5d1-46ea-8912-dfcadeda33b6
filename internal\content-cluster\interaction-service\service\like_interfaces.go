package service

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/pkg/errors"
)

// LikeService 点赞服务接口
type LikeService interface {
	// LikeContent 点赞/不喜欢或取消操作内容
	LikeContent(ctx context.Context, userKSUID string, req *dto.LikeContentRequest) (*dto.LikeContentResponse, *errors.Errors)

	// GetUserLikes 获取用户点赞记录（支持类型过滤）
	GetUserLikes(ctx context.Context, userKSUID string, req *dto.GetUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors)

	// GetOtherUserLikes 获取他人点赞记录
	GetOtherUserLikes(ctx context.Context, currentUserKSUID string, req *dto.GetOtherUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors)

	// GetContentLikeStats 获取内容点赞统计
	GetContentLikeStats(ctx context.Context, req *dto.GetContentLikeStatsRequest) (*dto.GetContentLikeStatsResponse, *errors.Errors)

	// GetUserLikeStatus 获取用户对内容的点赞状态
	GetUserLikeStatus(ctx context.Context, req *dto.GetUserLikeStatusRequest) (*dto.GetUserLikeStatusResponse, *errors.Errors)

	// BatchGetUserLikeStatus 批量获取用户点赞状态
	BatchGetUserLikeStatus(ctx context.Context, req *dto.BatchGetUserLikeStatusRequest) (*dto.BatchGetUserLikeStatusResponse, *errors.Errors)
}

// InternalLikeService 内部点赞服务接口（用于服务间调用）
type InternalLikeService interface {
	// CheckUserLikeStatus 检查用户对内容的点赞状态
	CheckUserLikeStatus(userKSUID, contentKSUID string) (likeType string, exists bool, err error)

	// GetContentLikeCounts 获取内容的点赞统计数据
	GetContentLikeCounts(contentKSUID string) (likeCount, dislikeCount int64, err error)

	// BatchGetContentLikeStats 批量获取内容点赞统计
	BatchGetContentLikeStats(contentKSUIDs []string) (map[string]ContentLikeStatsItem, error)

	// BatchCheckUserLikeStatus 批量检查用户点赞状态
	BatchCheckUserLikeStatus(userKSUID string, contentKSUIDs []string) (map[string]UserLikeStatusItem, error)
}

// ContentLikeStatsItem 内容点赞统计项
type ContentLikeStatsItem struct {
	ContentKSUID string `json:"content_ksuid"`
	LikeCount    int64  `json:"like_count"`
	DislikeCount int64  `json:"dislike_count"`
}

// UserLikeStatusItem 用户点赞状态项
type UserLikeStatusItem struct {
	Type       string `json:"type"`        // 点赞类型：like/dislike，空字符串表示未点赞
	IsLiked    bool   `json:"is_liked"`    // 是否点赞
	IsDisliked bool   `json:"is_disliked"` // 是否不喜欢
}
