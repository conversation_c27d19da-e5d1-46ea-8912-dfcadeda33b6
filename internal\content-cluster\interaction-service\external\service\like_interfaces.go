package service

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/pkg/errors"
)

// LikeService 点赞服务接口
type LikeService interface {
	// LikeContent 点赞/不喜欢或取消操作内容
	LikeContent(ctx context.Context, userKSUID string, req *dto.LikeContentRequest) (*dto.LikeContentResponse, *errors.Errors)

	// GetUserLikes 获取用户点赞记录（支持类型过滤）
	GetUserLikes(ctx context.Context, userKSUID string, req *dto.GetUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors)

	// GetOtherUserLikes 获取他人点赞记录
	GetOtherUserLikes(ctx context.Context, currentUserKSUID string, req *dto.GetOtherUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors)

	// GetContentLikeStats 获取内容点赞统计
	GetContentLikeStats(ctx context.Context, req *dto.GetContentLikeStatsRequest) (*dto.GetContentLikeStatsResponse, *errors.Errors)

	// GetUserLikeStatus 获取用户对内容的点赞状态
	GetUserLikeStatus(ctx context.Context, req *dto.GetUserLikeStatusRequest) (*dto.GetUserLikeStatusResponse, *errors.Errors)

	// BatchGetUserLikeStatus 批量获取用户点赞状态
	BatchGetUserLikeStatus(ctx context.Context, req *dto.BatchGetUserLikeStatusRequest) (*dto.BatchGetUserLikeStatusResponse, *errors.Errors)
}
