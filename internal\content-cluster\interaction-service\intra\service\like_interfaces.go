package service

// InternalLikeService 内部点赞服务接口（用于服务间调用）
type InternalLikeService interface {
	// CheckUserLikeStatus 检查用户对内容的点赞状态
	CheckUserLikeStatus(userKSUID, contentKSUID string) (likeType string, exists bool, err error)

	// GetContentLikeCounts 获取内容的点赞统计数据
	GetContentLikeCounts(contentKSUID string) (likeCount, dislikeCount int64, err error)

	// BatchGetContentLikeStats 批量获取内容点赞统计
	BatchGetContentLikeStats(contentKSUIDs []string) (map[string]ContentLikeStatsItem, error)

	// BatchCheckUserLikeStatus 批量检查用户点赞状态
	BatchCheckUserLikeStatus(userKSUID string, contentKSUIDs []string) (map[string]UserLikeStatusItem, error)
}

// ContentLikeStatsItem 内容点赞统计项
type ContentLikeStatsItem struct {
	ContentKSUID string `json:"content_ksuid"`
	LikeCount    int64  `json:"like_count"`
	DislikeCount int64  `json:"dislike_count"`
}

// UserLikeStatusItem 用户点赞状态项
type UserLikeStatusItem struct {
	Type       string `json:"type"`        // 点赞类型：like/dislike，空字符串表示未点赞
	IsLiked    bool   `json:"is_liked"`    // 是否点赞
	IsDisliked bool   `json:"is_disliked"` // 是否不喜欢
}
