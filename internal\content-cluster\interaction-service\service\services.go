package service

// Services 服务层集合
type Services struct {
	// Album相关服务
	AlbumService        AlbumService
	AlbumContentService AlbumContentService

	// Favorite相关服务
	FavoriteFolderService   FavoriteFolderService
	FavoriteItemService     FavoriteItemService
	InternalFavoriteService InternalFavoriteService

	// Like相关服务
	LikeService         LikeService
	InternalLikeService InternalLikeService

	// PlayHistory相关服务
	PlayHistoryService         PlayHistoryService
	InternalPlayHistoryService InternalPlayHistoryService

	// 其他交互相关服务（预留）
	// CommentService  CommentService
	// ShareService    ShareService
	// RewardService   RewardService
}

// NewServices 创建服务层集合实例
func NewServices(
	// Album相关
	albumService AlbumService,
	albumContentService AlbumContentService,
	// Favorite相关
	favoriteFolderService FavoriteFolderService,
	favoriteItemService FavoriteItemService,
	internalFavoriteService InternalFavoriteService,
	// Like相关
	likeService LikeService,
	internalLikeService InternalLikeService,
	// PlayHistory相关
	playHistoryService PlayHistoryService,
	internalPlayHistoryService InternalPlayHistoryService,
) *Services {
	return &Services{
		// Album相关
		AlbumService:        albumService,
		AlbumContentService: albumContentService,

		// Favorite相关
		FavoriteFolderService:   favoriteFolderService,
		FavoriteItemService:     favoriteItemService,
		InternalFavoriteService: internalFavoriteService,

		// Like相关
		LikeService:         likeService,
		InternalLikeService: internalLikeService,

		// PlayHistory相关
		PlayHistoryService:         playHistoryService,
		InternalPlayHistoryService: internalPlayHistoryService,

		// 其他交互相关（暂时为nil，后续实现）
		// CommentService: nil,
		// ShareService:   nil,
		// RewardService:  nil,
	}
}

// GetFavoriteFolderService 获取收藏夹服务
func (s *Services) GetFavoriteFolderService() FavoriteFolderService {
	return s.FavoriteFolderService
}

// GetFavoriteItemService 获取收藏项服务
func (s *Services) GetFavoriteItemService() FavoriteItemService {
	return s.FavoriteItemService
}

// GetInternalFavoriteService 获取内部收藏服务
func (s *Services) GetInternalFavoriteService() InternalFavoriteService {
	return s.InternalFavoriteService
}

// GetAlbumService 获取合集服务
func (s *Services) GetAlbumService() AlbumService {
	return s.AlbumService
}

// GetAlbumContentService 获取合集内容服务
func (s *Services) GetAlbumContentService() AlbumContentService {
	return s.AlbumContentService
}

// GetLikeService 获取点赞服务
func (s *Services) GetLikeService() LikeService {
	return s.LikeService
}

// GetInternalLikeService 获取内部点赞服务
func (s *Services) GetInternalLikeService() InternalLikeService {
	return s.InternalLikeService
}

// GetPlayHistoryService 获取播放历史服务
func (s *Services) GetPlayHistoryService() PlayHistoryService {
	return s.PlayHistoryService
}

// GetInternalPlayHistoryService 获取内部播放历史服务
func (s *Services) GetInternalPlayHistoryService() InternalPlayHistoryService {
	return s.InternalPlayHistoryService
}
