package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

type LikeHandler struct {
	likeService service.LikeService
}

func NewLikeHandler(likeService service.LikeService) *LikeHandler {
	return &LikeHandler{
		likeService: likeService,
	}
}

func (h *LikeHandler) LikeContent(c *gin.Context) {
	// 获取用户ID
	userKSUID := ksuid.GetKSUID(c)

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到点赞/不喜欢内容请求")

	// 解析请求参数
	var req dto.LikeContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("解析点赞/不喜欢请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.LikeContent(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Str("type", req.Type).
			Msg("点赞/不喜欢内容失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Str("type", response.Type).
		Bool("is_active", response.IsActive).
		Int64("like_count", response.LikeCount).
		Int64("dislike_count", response.DislikeCount).
		Msg("点赞/不喜欢内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetMyLikes 获取用户点赞记录
// @Summary 获取用户点赞记录
// @Description 获取当前用户的点赞记录，支持类型和内容类型过滤
// @Tags 内容点赞
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param type query string false "类型过滤：like/dislike"
// @Param content_type query string false "内容类型过滤：video/short/anime/novel"
// @Param page query int false "页码，从1开始" default(1)
// @Param page_size query int false "每页数量，默认20" default(20)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.GetUserLikesResponse} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "参数错误"
// @Failure 401 {object} globalTypes.GlobalResponse "未授权"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器错误"
// @Router /api/v1/interaction/like/my [get]
func (h *LikeHandler) GetMyLikes(c *gin.Context) {
	// 获取用户ID
	userKSUID := ksuid.GetKSUID(c)

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取用户点赞记录请求")

	// 解析查询参数
	var req dto.GetUserLikesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("解析获取用户点赞记录请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.GetUserLikes(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("type", req.Type).
			Str("content_type", req.ContentType).
			Msg("获取用户点赞记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("type", req.Type).
		Str("content_type", req.ContentType).
		Int64("total", response.Total).
		Int("count", len(response.Likes)).
		Msg("获取用户点赞记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetOtherUserLikes 获取他人点赞记录
// @Summary 获取他人点赞记录
// @Description 获取指定用户的点赞记录，会检查用户隐私设置
// @Tags 内容点赞
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param user_ksuid query string true "目标用户KSUID"
// @Param type query string false "类型过滤：like/dislike"
// @Param content_type query string false "内容类型过滤：video/short/anime/novel"
// @Param page query int false "页码，从1开始" default(1)
// @Param page_size query int false "每页数量，默认20" default(20)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.GetUserLikesResponse} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "参数错误"
// @Failure 401 {object} globalTypes.GlobalResponse "未授权"
// @Failure 10068 {object} globalTypes.GlobalResponse "用户设置了点赞记录隐私"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器错误"
// @Router /api/v1/interaction/like/user [get]
func (h *LikeHandler) GetOtherUserLikes(c *gin.Context) {
	// 获取当前用户ID
	currentUserKSUID, exists := c.Get("user_ksuid")
	if !exists {
		log.Warn().
			Str("method", c.Request.Method).
			Str("path", c.Request.URL.Path).
			Msg("用户未认证")
		c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
			Code: errors.INVALID_TOKEN,
		})
		return
	}

	CurrentUserKSUID := currentUserKSUID.(string)

	log.Info().
		Str("current_user_ksuid", CurrentUserKSUID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取他人点赞记录请求")

	// 解析查询参数
	var req dto.GetOtherUserLikesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Str("current_user_ksuid", CurrentUserKSUID).
			Msg("解析获取他人点赞记录请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.GetOtherUserLikes(c.Request.Context(), CurrentUserKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("current_user_ksuid", CurrentUserKSUID).
			Str("target_user_ksuid", req.UserKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("type", req.Type).
			Str("content_type", req.ContentType).
			Msg("获取他人点赞记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("current_user_ksuid", CurrentUserKSUID).
		Str("target_user_ksuid", req.UserKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("type", req.Type).
		Str("content_type", req.ContentType).
		Int64("total", response.Total).
		Int("count", len(response.Likes)).
		Msg("获取他人点赞记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetContentLikeStats 获取内容点赞统计
// @Summary 获取内容点赞统计
// @Description 获取指定内容的点赞统计信息
// @Tags 内容点赞
// @Accept json
// @Produce json
// @Param content_ksuid query string true "内容KSUID"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.GetContentLikeStatsResponse} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器错误"
// @Router /api/v1/interaction/like/content/stats [get]
func (h *LikeHandler) GetContentLikeStats(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取内容点赞统计请求")

	// 解析查询参数
	var req dto.GetContentLikeStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析获取内容点赞统计请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.GetContentLikeStats(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("content_ksuid", req.ContentKSUID).
			Msg("获取内容点赞统计失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("content_ksuid", req.ContentKSUID).
		Int64("like_count", response.LikeCount).
		Int64("dislike_count", response.DislikeCount).
		Msg("获取内容点赞统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetUserLikeStatus 获取用户点赞状态
// @Summary 获取用户点赞状态
// @Description 获取用户对指定内容的点赞状态
// @Tags 内容点赞
// @Accept json
// @Produce json
// @Param user_ksuid query string true "用户KSUID"
// @Param content_ksuid query string true "内容KSUID"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.GetUserLikeStatusResponse} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器错误"
// @Router /api/v1/like/status [get]
func (h *LikeHandler) GetUserLikeStatus(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取用户点赞状态请求")

	// 解析查询参数
	var req dto.GetUserLikeStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析获取用户点赞状态请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.GetUserLikeStatus(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("target_user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("获取用户点赞状态失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("target_user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("type", response.Type).
		Bool("is_liked", response.IsLiked).
		Bool("is_disliked", response.IsDisliked).
		Msg("获取用户点赞状态成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// BatchGetUserLikeStatus 批量获取用户点赞状态
// @Summary 批量获取用户点赞状态
// @Description 批量获取用户对多个内容的点赞状态
// @Tags 内容点赞
// @Accept json
// @Produce json
// @Param request body dto.BatchGetUserLikeStatusRequest true "批量获取请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchGetUserLikeStatusResponse} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "参数错误"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器错误"
// @Router /api/v1/like/status/batch [post]
func (h *LikeHandler) BatchGetUserLikeStatus(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到批量获取用户点赞状态请求")

	// 解析请求参数
	var req dto.BatchGetUserLikeStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析批量获取用户点赞状态请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.BatchGetUserLikeStatus(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("target_user_ksuid", req.UserKSUID).
			Int("content_count", len(req.ContentKSUIDs)).
			Msg("批量获取用户点赞状态失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("target_user_ksuid", req.UserKSUID).
		Int("content_count", len(req.ContentKSUIDs)).
		Int("result_count", len(response.Statuses)).
		Msg("批量获取用户点赞状态成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}
